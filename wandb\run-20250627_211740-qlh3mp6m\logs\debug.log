2025-06-27 21:17:40,390 INFO    MainThread:3068 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_setup.py:_flush():81] Configure stats pid to 3068
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\settings
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\run-20250627_211740-qlh3mp6m\logs\debug.log
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\run-20250627_211740-qlh3mp6m\logs\debug-internal.log
2025-06-27 21:17:40,391 INFO    MainThread:3068 [wandb_init.py:init():831] calling init triggers
2025-06-27 21:17:40,392 INFO    MainThread:3068 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'n_proc': 32, 'min_inference_size': 29, 'timestep_limit': 1000000000, 'exp_buffer_size': 150000, 'ts_per_iteration': 50000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': (256, 256, 256), 'critic_layer_sizes': (256, 256, 256), 'ppo_epochs': 2, 'ppo_batch_size': 50000, 'ppo_minibatch_size': 25000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.2, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0003, 'critic_lr': 0.0003, 'shm_buffer_size': 8192, '_wandb': {}}
2025-06-27 21:17:40,392 INFO    MainThread:3068 [wandb_init.py:init():872] starting backend
2025-06-27 21:17:40,630 INFO    MainThread:3068 [wandb_init.py:init():875] sending inform_init request
2025-06-27 21:17:40,686 INFO    MainThread:3068 [wandb_init.py:init():883] backend started and connected
2025-06-27 21:17:40,690 INFO    MainThread:3068 [wandb_init.py:init():956] updated telemetry
2025-06-27 21:17:40,692 INFO    MainThread:3068 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-27 21:17:41,308 INFO    MainThread:3068 [wandb_init.py:init():1032] starting run threads in backend
2025-06-27 21:17:41,589 INFO    MainThread:3068 [wandb_run.py:_console_start():2453] atexit reg
2025-06-27 21:17:41,589 INFO    MainThread:3068 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-27 21:17:41,589 INFO    MainThread:3068 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-27 21:17:41,590 INFO    MainThread:3068 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-27 21:17:41,594 INFO    MainThread:3068 [wandb_init.py:init():1078] run started, returning control to user process
