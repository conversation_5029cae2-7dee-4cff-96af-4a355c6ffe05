import numpy as np
from rlgym_sim.utils import RewardFunction
from rlgym_sim.utils.gamestates import GameState, PlayerData
from rlgym_sim.utils.common_values import CAR_MAX_SPEED

class InAirReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        return 1.0 if not player.on_ground else 0.0

class SpeedTowardBallReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        player_vel = player.car_data.linear_velocity
        pos_diff = state.ball.position - player.car_data.position
        dist_to_ball = np.linalg.norm(pos_diff)
        if dist_to_ball == 0:
            return 0.0
        dir_to_ball = pos_diff / dist_to_ball
        speed_toward_ball = np.dot(player_vel, dir_to_ball)
        return max(0.0, speed_toward_ball / CAR_MAX_SPEED)
