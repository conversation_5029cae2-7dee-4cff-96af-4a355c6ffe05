import numpy as np
from rlgym_sim.utils import RewardFunction
from rlgym_sim.utils.gamestates import GameState, PlayerData
from rlgym_sim.utils.common_values import CAR_MAX_SPEED, SIDE_WALL_X, BACK_NET_Y

class InAirReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        return 1.0 if not player.on_ground else 0.0

class SpeedTowardBallReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        player_vel = player.car_data.linear_velocity
        pos_diff = state.ball.position - player.car_data.position
        dist_to_ball = np.linalg.norm(pos_diff)
        if dist_to_ball == 0:
            return 0.0
        dir_to_ball = pos_diff / dist_to_ball
        speed_toward_ball = np.dot(player_vel, dir_to_ball)
        return max(0.0, speed_toward_ball / CAR_MAX_SPEED)


class CarrotsReward(RewardFunction):
    """
    Comprehensive reward system (0-100 points) for encouraging good Rocket League gameplay.
    Rewards going to ball, possession, field control, and scoring while penalizing bad play.
    """
    def __init__(self):
        super().__init__()
        self.last_ball_touch_player = None
        self.possession_time = {}
        self.last_ball_position = None

    def reset(self, initial_state: GameState):
        self.last_ball_touch_player = None
        self.possession_time = {}
        self.last_ball_position = initial_state.ball.position.copy()

    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        reward = 0.0

        # Initialize possession tracking for this player
        if player.car_id not in self.possession_time:
            self.possession_time[player.car_id] = 0

        # === BALL PROXIMITY CARROTS (0-15 points) ===
        ball_pos = state.ball.position
        player_pos = player.car_data.position
        dist_to_ball = np.linalg.norm(ball_pos - player_pos)

        # Reward being close to ball (exponential decay)
        max_reward_distance = 500  # Units where you get max reward
        proximity_reward = 15 * np.exp(-dist_to_ball / max_reward_distance)
        reward += proximity_reward

        # === BALL POSSESSION CARROTS (0-25 points) ===
        # Check if player is touching/near ball (possession)
        is_possessing = dist_to_ball < 200  # Close enough to be "possessing"

        if is_possessing:
            self.possession_time[player.car_id] += 1
            self.last_ball_touch_player = player.car_id
            # Reward active possession
            reward += 10

            # Bonus for sustained possession (up to 15 more points)
            possession_bonus = min(15, self.possession_time[player.car_id] * 0.5)
            reward += possession_bonus
        else:
            # Decay possession time when not touching ball
            self.possession_time[player.car_id] = max(0, self.possession_time[player.car_id] - 0.5)

        # === FIELD CONTROL CARROTS (0-20 points) ===
        # Reward keeping ball on opponent's side
        field_center_y = 0
        opponent_goal_y = BACK_NET_Y if player.team_num == 0 else -BACK_NET_Y

        # Ball position relative to field center
        ball_field_position = (ball_pos[1] - field_center_y) / BACK_NET_Y

        if player.team_num == 0:  # Blue team
            if ball_pos[1] > field_center_y:  # Ball on orange side
                field_control_reward = 20 * (ball_field_position)
                reward += max(0, field_control_reward)
        else:  # Orange team
            if ball_pos[1] < field_center_y:  # Ball on blue side
                field_control_reward = 20 * (-ball_field_position)
                reward += max(0, field_control_reward)

        # === OFFENSIVE PLAY CARROTS (0-25 points) ===
        # Reward moving ball toward opponent goal
        if self.last_ball_position is not None:
            ball_movement = ball_pos - self.last_ball_position

            # Direction to opponent goal
            if player.team_num == 0:  # Blue team attacking orange goal
                goal_direction = np.array([0, 1, 0])  # Positive Y
            else:  # Orange team attacking blue goal
                goal_direction = np.array([0, -1, 0])  # Negative Y

            # Reward if ball moved toward opponent goal
            ball_toward_goal = np.dot(ball_movement, goal_direction)
            if ball_toward_goal > 0 and is_possessing:
                offensive_reward = min(25, ball_toward_goal * 5)
                reward += offensive_reward

        # === SPEED AND AGGRESSION CARROTS (0-10 points) ===
        # Reward high speed when going toward ball or goal
        player_speed = np.linalg.norm(player.car_data.linear_velocity)
        speed_ratio = player_speed / CAR_MAX_SPEED

        # Direction to ball
        if dist_to_ball > 0:
            dir_to_ball = (ball_pos - player_pos) / dist_to_ball
            vel_toward_ball = np.dot(player.car_data.linear_velocity, dir_to_ball)

            if vel_toward_ball > 0:  # Moving toward ball
                speed_reward = 10 * speed_ratio * (vel_toward_ball / player_speed)
                reward += speed_reward

        # === POSSESSION LOSS PENALTY (-15 points) ===
        # Penalty if opponent gains possession after you had it
        if (self.last_ball_touch_player == player.car_id and
            not is_possessing and dist_to_ball > 400):
            # Check if any opponent is now closer to ball
            for other_player in state.players:
                if other_player.team_num != player.team_num:
                    other_dist = np.linalg.norm(ball_pos - other_player.car_data.position)
                    if other_dist < dist_to_ball and other_dist < 200:
                        reward -= 15  # Possession loss penalty
                        break

        # === DEFENSIVE POSITIONING PENALTY (-10 points) ===
        # Penalty for being too far from action when ball is on your side
        own_goal_y = -BACK_NET_Y if player.team_num == 0 else BACK_NET_Y

        # If ball is on your defensive side and you're far away
        if ((player.team_num == 0 and ball_pos[1] < field_center_y) or
            (player.team_num == 1 and ball_pos[1] > field_center_y)):
            if dist_to_ball > 1000:  # Too far from defensive action
                reward -= 10

        # Update last ball position for next frame
        self.last_ball_position = ball_pos.copy()

        # Clamp reward to 0-100 range
        return max(0, min(100, reward))
