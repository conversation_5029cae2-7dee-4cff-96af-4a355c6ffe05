{"cumulative_timesteps": 2301076, "cumulative_model_updates": 270, "policy_average_reward": 0.7324980548715085, "epoch": 45, "ts_since_last_save": 100062, "reward_running_stats": {"mean": [0.0407920703291893], "var": [2705.24853515625], "shape": [1], "count": 6900}, "wandb_run_id": "qlh3mp6m", "wandb_project": "rlgym-ppo", "wandb_entity": "isa1ah56dev-pinterest", "wandb_group": "unnamed-runs", "wandb_config": {"n_proc": 32, "min_inference_size": 29, "timestep_limit": 1000000000, "exp_buffer_size": 150000, "ts_per_iteration": 50000, "standardize_returns": true, "standardize_obs": false, "policy_layer_sizes": [256, 256, 256], "critic_layer_sizes": [256, 256, 256], "ppo_epochs": 2, "ppo_batch_size": 50000, "ppo_minibatch_size": 25000, "ppo_ent_coef": 0.01, "ppo_clip_range": 0.2, "gae_lambda": 0.95, "gae_gamma": 0.99, "policy_lr": 0.0003, "critic_lr": 0.0003, "shm_buffer_size": 8192}}