import socket
import json
import time

from rlgym_sim.utils.gamestates import GameState

UDP_IP = "127.0.0.1"
UDP_PORT = 9273 # Default RocketSimVis port

sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)  # UDP
sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # Increase send buffer
sock.settimeout(0.001)  # Very short timeout to prevent blocking

# Frame rate limiting for smoother performance
last_send_time = 0
min_frame_interval = 1.0 / 60.0  # Limit to 60 FPS max

def write_physobj(physobj):
	j = {}
	
	j['pos'] = physobj.position.tolist()
	j['forward'] = physobj.forward().tolist()
	j['up'] = physobj.up().tolist()
	j['vel'] = physobj.linear_velocity.tolist()
	j['ang_vel'] = physobj.angular_velocity.tolist()
	
	return j

def write_car(player):
	j = {}
	
	j['team_num'] = int(player.team_num)
	j['phys'] = write_physobj(player.car_data)
	
	j['boost_amount'] = player.boost_amount * 100
	j['on_ground'] = bool(player.on_ground)
	j['is_demoed'] = bool(player.is_demoed)
	j['has_flip'] = bool(player.has_flip)

	return j

def send_state_to_rocketsimvis(gs: GameState):
	global last_send_time

	# Frame rate limiting - skip frames if sending too frequently
	current_time = time.time()
	if current_time - last_send_time < min_frame_interval:
		return

	try:
		j = {}

		# Send ball
		j['ball_phys'] = write_physobj(gs.ball)

		# Send cars
		j['cars'] = []
		for player in gs.players:
			j['cars'].append(write_car(player))

		# Send boost pad states
		j['boost_pad_states'] = gs.boost_pads.tolist()

		# Non-blocking send with error handling
		data = json.dumps(j).encode('utf-8')
		sock.sendto(data, (UDP_IP, UDP_PORT))
		last_send_time = current_time

	except (socket.error, OSError):
		# Silently ignore network errors to prevent training interruption
		pass